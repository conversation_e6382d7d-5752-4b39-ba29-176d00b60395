#!/usr/bin/env python3
"""
API路由模块
定义Flask应用的所有路由和端点
专注于HTTP层面的处理：路由定义、请求解析、响应格式化
"""

import logging
from flask import Flask, request, jsonify

from core.face_processor import FaceProcessor
from task.cache_manager import FaceAnalyzerCacheManager
from .service import analyze_and_generate_webp_animation, analyze_and_generate_single_webp
from .handlers import validate_json_request
from utils.file_service import validate_input_file, normalize_output_path

logger = logging.getLogger(__name__)


def create_app() -> Flask:
    """
    创建Flask应用实例

    Returns:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)

    # 初始化核心组件
    face_processor = FaceProcessor()
    cache_manager = FaceAnalyzerCacheManager()

    @app.route('/analyze', methods=['POST'])
    def analyze_video():
        """视频分析API端点"""
        # 解析和验证JSON请求
        data = request.get_json()
        is_valid, error_response = validate_json_request(
            data, ['input_path', 'output_path']
        )
        if not is_valid:
            return jsonify(error_response), 400

        input_path = data.get('input_path')
        output_path = data.get('output_path')
        resolution = data.get('resolution')

        # 验证输入文件
        validate_input_file(input_path)

        # 规范化输出路径
        output_path = normalize_output_path(output_path)

        # 记录请求开始
        log_msg = f"开始处理请求: {input_path} -> {output_path}"
        if resolution:
            log_msg += f", 分辨率: {resolution}"
        logger.info(log_msg)

        # 调用业务逻辑
        result = analyze_and_generate_webp_animation(
            face_processor, cache_manager, input_path, output_path, resolution
        )

        logger.info(f"处理完成: {result}")
        return jsonify(result)

    @app.route('/analyze_frame', methods=['POST'])
    def analyze_video_frame():
        """视频单帧分析API端点"""
        # 解析和验证JSON请求
        data = request.get_json()
        is_valid, error_response = validate_json_request(
            data, ['input_path', 'output_path']
        )
        if not is_valid:
            return jsonify(error_response), 400

        input_path = data.get('input_path')
        output_path = data.get('output_path')
        resolution = data.get('resolution')

        # 验证输入文件
        validate_input_file(input_path)

        # 规范化输出路径
        output_path = normalize_output_path(output_path)

        # 记录请求开始
        log_msg = f"开始处理单帧请求: {input_path} -> {output_path}"
        if resolution:
            log_msg += f", 分辨率: {resolution}"
        logger.info(log_msg)

        # 调用业务逻辑
        result = analyze_and_generate_single_webp(
            face_processor, cache_manager, input_path, output_path, resolution
        )

        logger.info(f"单帧处理完成: {result}")
        return jsonify(result)



    return app