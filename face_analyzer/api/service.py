#!/usr/bin/env python3
"""
API业务逻辑服务层
处理视频分析、WebP生成等核心业务逻辑，与HTTP层解耦
"""

import os
import logging
from typing import Optional, Dict, Any

from config.settings import Config
from core.face_processor import FaceProcessor
from task.webp_animation import generate_webp_animation
from task.webp_single import generate_single_webp
from task.cache_manager import FaceAnalyzerCacheManager, process_with_cache

logger = logging.getLogger(__name__)


def process_video_with_cache(face_processor: FaceProcessor, cache_manager: FaceAnalyzerCacheManager,
                           input_path: str, operation_name: str = "视频处理"):
    """
    统一的视频处理逻辑 - 包含缓存检查、视频处理、缓存写入

    Args:
        face_processor: 人脸处理器实例
        cache_manager: 缓存管理器实例
        input_path: 输入视频路径
        operation_name: 操作名称，用于日志记录

    Returns:
        ProcessResult: 处理结果对象

    Raises:
        RuntimeError: 当视频处理失败时
    """
    # === 缓存检查逻辑（复用BatchManager的相同流程） ===
    cached_result = cache_manager.get_cached_result(input_path)
    if cached_result:
        result = process_with_cache(input_path, cached_result)
    else:
        # === 视频处理逻辑（复用BatchManager的相同流程） ===
        logger.info(f"开始{operation_name}: {input_path}")
        result = face_processor.process_single_video(input_path)
        logger.info(f"{operation_name}结果: success={result.success}, valid_frames={len(result.valid_frames) if result.valid_frames else 0}")

        # === 缓存写入逻辑（复用BatchManager的相同流程） ===
        if result.success:
            cache_manager.cache_result(input_path, result.valid_frames)

    # 检查处理结果
    if not result.success:
        logger.error(f"{operation_name}失败: {input_path}")
        raise RuntimeError(f"{operation_name}失败")

    return result

def analyze_and_generate_webp_animation(face_processor: FaceProcessor, cache_manager: FaceAnalyzerCacheManager,
                                      input_path: str, output_path: str,
                                      resolution: Optional[str] = None) -> Dict[str, Any]:
    """
    分析视频并生成WebP动画 - webp_generator兼容接口

    Args:
        face_processor: 人脸处理器实例
        cache_manager: 缓存管理器实例
        input_path: 输入视频路径
        output_path: 输出WebP文件路径
        resolution: 可选的分辨率参数，格式如 "640x480"

    Returns:
        dict: webp_generator兼容的响应格式
    """
    # 使用统一的视频处理逻辑（复用BatchManager的相同流程）
    result = process_video_with_cache(face_processor, cache_manager, input_path, "视频处理")

    # 获取最高得分的帧索引列表
    valid_frames = result.valid_frames
    if not valid_frames:
        raise RuntimeError("未能收集到任何帧")

    # 获取最高得分的帧索引
    max_score = max(valid_frames.keys())
    frame_indices = valid_frames[max_score]
    first_qualifying_frame_idx = min(frame_indices)

    # 获取视频信息
    video_info = get_video_info(input_path)
    fps = video_info['fps']

    # 转换为webp_generator兼容的时间戳格式
    timestamp = convert_frame_idx_to_timestamp(first_qualifying_frame_idx, fps)

    # 解析分辨率参数
    if resolution:
        from utils.image_utils import parse_resolution
        target_width, target_height = parse_resolution(resolution)
        logger.info(f"WebP动画将调整到分辨率: {target_width}x{target_height}")

    # 生成WebP动画
    generate_webp_animation(input_path, result, output_path, resolution=resolution)
    frames_collected = len(frame_indices)



    # 返回webp_generator兼容格式
    return {
        "success": True,
        "timestamp": timestamp,
        "output_path": output_path,
        "frames_collected": frames_collected,
        "first_qualifying_frame": first_qualifying_frame_idx,
        "video_info": {
            "fps": fps,
            "total_frames": video_info['total_frames'],
            "duration_seconds": video_info['duration_seconds']
        }
    }

def analyze_and_generate_single_webp(face_processor: FaceProcessor, cache_manager: FaceAnalyzerCacheManager,
                                    input_path: str, output_path: str,
                                    resolution: Optional[str] = None) -> Dict[str, Any]:
    """
    分析视频并生成单帧WebP图片 - webp_generator兼容接口

    Args:
        face_processor: 人脸处理器实例
        cache_manager: 缓存管理器实例
        input_path: 输入视频路径
        output_path: 输出WebP文件路径
        resolution: 可选的分辨率参数，格式如 "640x480"

    Returns:
        dict: webp_generator兼容的响应格式
    """
    # 使用统一的视频处理逻辑（复用BatchManager的相同流程）
    result = process_video_with_cache(face_processor, cache_manager, input_path, "视频处理(单帧)")

    # 获取最高得分的帧索引
    valid_frames = result.valid_frames
    if not valid_frames:
        raise RuntimeError("未能获取到目标帧")

    max_score = max(valid_frames.keys())
    frame_indices = valid_frames[max_score]
    first_qualifying_frame_idx = min(frame_indices)

    # 获取视频信息
    video_info = get_video_info(input_path)
    fps = video_info['fps']

    # 转换为webp_generator兼容的时间戳格式
    timestamp = convert_frame_idx_to_timestamp(first_qualifying_frame_idx, fps)

    # 解析分辨率参数
    if resolution:
        from utils.image_utils import parse_resolution
        target_width, target_height = parse_resolution(resolution)
        logger.info(f"单帧WebP将调整到分辨率: {target_width}x{target_height}")

    # 生成单帧WebP
    generate_single_webp(input_path, result, output_path, resolution=resolution)

    # 返回webp_generator兼容格式
    return {
        "success": True,
        "timestamp": timestamp,
        "output_path": output_path,
        "frame_index": first_qualifying_frame_idx,
        "video_info": {
            "fps": fps,
            "total_frames": video_info['total_frames'],
            "duration_seconds": video_info['duration_seconds']
        }
    }



# ============================================================================
# 工具函数 - 原VideoAnalysisService的私有方法实现
# ============================================================================

def convert_frame_idx_to_timestamp(frame_idx: int, fps: float) -> str:
    """
    将帧索引转换为webp_generator兼容的时间戳格式

    Args:
        frame_idx: 帧索引
        fps: 视频帧率

    Returns:
        str: HH:MM:SS格式的时间戳
    """
    seconds = frame_idx / fps
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"


def get_video_info(video_path: str) -> Dict[str, Any]:
    """
    获取视频信息

    Args:
        video_path: 视频文件路径

    Returns:
        dict: 包含fps、total_frames、duration_seconds的视频信息
    """
    try:
        import cv2
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频文件: {video_path}")

        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration_seconds = total_frames / fps if fps > 0 else 0

        cap.release()

        return {
            'fps': fps,
            'total_frames': total_frames,
            'duration_seconds': duration_seconds
        }
    except Exception as e:
        logger.warning(f"获取视频信息失败，使用默认值: {e}")
        return {
            'fps': 30.0,
            'total_frames': 1800,
            'duration_seconds': 60.0
        }



