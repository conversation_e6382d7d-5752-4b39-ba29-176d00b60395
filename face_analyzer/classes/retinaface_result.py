#!/usr/bin/env python3
"""
RetinaFace检测结果数据类
"""

from typing import List, Tuple, Optional, Dict, Any
import numpy as np


class RetinaFaceResult:
    """RetinaFace单个人脸检测结果类"""

    def __init__(self, face_id: int, confidence: float, bbox: List[float],
                 landmarks: Optional[np.ndarray] = None, metadata: Optional[Dict[str, Any]] = None):
        """
        初始化RetinaFace检测结果

        Args:
            face_id (int): 人脸ID（在当前帧中的索引）
            confidence (float): 检测置信度
            bbox (List[float]): 边界框信息 [x1, y1, x2, y2]
            landmarks (Optional[np.ndarray]): 关键点信息 (5个关键点，每个点包含x, y坐标)
            metadata (Optional[Dict[str, Any]]): 扩展信息
        """
        self.face_id = face_id
        self.confidence = confidence
        self.bbox = bbox
        self.landmarks = landmarks
        self.metadata = metadata

        # 计算并存储常用属性
        self.x1 = self.bbox[0]
        self.y1 = self.bbox[1]
        self.x2 = self.bbox[2]
        self.y2 = self.bbox[3]
        self.width = self.x2 - self.x1
        self.height = self.y2 - self.y1
        self.area = self.width * self.height
        self.center = ((self.x1 + self.x2) / 2, (self.y1 + self.y2) / 2)

    def get_landmark_points(self) -> Optional[Dict[str, Tuple[float, float]]]:
        """
        获取关键点字典
        
        Returns:
            Dict[str, Tuple[float, float]]: 关键点字典，包含left_eye, right_eye, nose, left_mouth, right_mouth
        """
        if self.landmarks is None or len(self.landmarks) < 10:
            return None
        
        # RetinaFace返回5个关键点：左眼、右眼、鼻子、左嘴角、右嘴角
        return {
            'left_eye': (float(self.landmarks[0]), float(self.landmarks[1])),
            'right_eye': (float(self.landmarks[2]), float(self.landmarks[3])),
            'nose': (float(self.landmarks[4]), float(self.landmarks[5])),
            'left_mouth': (float(self.landmarks[6]), float(self.landmarks[7])),
            'right_mouth': (float(self.landmarks[8]), float(self.landmarks[9]))
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            Dict[str, Any]: 字典表示
        """
        result = {
            'face_id': self.face_id,
            'confidence': self.confidence,
            'bbox': self.bbox,
            'width': self.width,
            'height': self.height,
            'area': self.area,
            'center': self.center
        }
        
        if self.landmarks is not None:
            result['landmarks'] = self.landmarks.tolist()
            result['landmark_points'] = self.get_landmark_points()
        
        if self.metadata is not None:
            result['metadata'] = self.metadata
            
        return result


def create_retinaface_list(detection_result):
    """
    将检测结果转换为RetinaFaceResult实例列表

    Args:
        detection_result: RetinaFace检测结果

    Returns:
        List[RetinaFaceResult]: RetinaFaceResult实例列表
    """
    face_results = []

    if detection_result is not None and len(detection_result) == 2:
        dets, landms = detection_result

        if dets is not None and len(dets) > 0:
            for i in range(len(dets)):
                det = dets[i]
                if len(det) < 5:
                    continue

                x1, y1, x2, y2, confidence = det[:5]

                # 获取对应的关键点
                landmarks = None
                if landms is not None and i < len(landms):
                    landmarks = landms[i]

                if landmarks is not None:
                    # 使用标准__init__方法创建RetinaFaceResult实例
                    face_result = RetinaFaceResult(
                        face_id=i + 1,
                        confidence=float(confidence),
                        bbox=[float(x1), float(y1), float(x2), float(y2)],
                        landmarks=landmarks
                    )
                    face_results.append(face_result)

    return face_results
