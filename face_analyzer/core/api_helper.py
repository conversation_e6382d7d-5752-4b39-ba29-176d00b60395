#!/usr/bin/env python3
"""
API辅助函数模块
处理视频分析、WebP生成等核心业务逻辑，与HTTP层解耦
"""

import logging
import cv2
from typing import Optional, Dict, Any

from .face_processor import FaceProcessor
from task.webp_animation import generate_webp_animation
from task.webp_single import generate_single_webp
from task.cache_manager import FaceAnalyzerCacheManager, process_with_cache

logger = logging.getLogger(__name__)

def process_video_with_cache(face_processor: FaceProcessor, cache_manager: FaceAnalyzerCacheManager,
                           input_path: str, operation_name: str = "视频处理"):
    """
    统一的视频处理逻辑 - 包含缓存检查、视频处理、缓存写入

    Args:
        face_processor: 人脸处理器实例
        cache_manager: 缓存管理器实例
        input_path: 输入视频路径
        operation_name: 操作名称，用于日志记录

    Returns:
        ProcessResult: 处理结果对象

    Raises:
        RuntimeError: 当视频处理失败时
    """
    # === 缓存检查逻辑（复用BatchManager的相同流程） ===
    cached_result = cache_manager.get_cached_result(input_path)
    if cached_result:
        result = process_with_cache(input_path, cached_result)
    else:
        # === 视频处理逻辑（复用BatchManager的相同流程） ===
        logger.info(f"开始{operation_name}: {input_path}")
        result = face_processor.process_video_with_segmentation(input_path)
        logger.info(f"{operation_name}结果: success={result.success}, valid_frames={len(result.valid_frames) if result.valid_frames else 0}")

        # === 缓存写入逻辑（复用BatchManager的相同流程） ===
        if result.success:
            cache_manager.cache_result(input_path, result.valid_frames)

    # 检查处理结果
    if not result.success:
        logger.error(f"{operation_name}失败: {input_path}")
        raise RuntimeError(f"{operation_name}失败")

    return result

def analyze_and_generate_webp_animation(face_processor: FaceProcessor, cache_manager: FaceAnalyzerCacheManager,
                                      input_path: str, output_path: str,
                                      resolution: Optional[str] = None) -> Dict[str, Any]:
    """
    分析视频并生成WebP动画 - webp_generator兼容接口

    Args:
        face_processor: 人脸处理器实例
        cache_manager: 缓存管理器实例
        input_path: 输入视频路径
        output_path: 输出WebP文件路径
        resolution: 可选的分辨率参数，格式如 "640x480"

    Returns:
        dict: webp_generator兼容的响应格式
    """
    # 使用统一的视频处理逻辑（复用BatchManager的相同流程）
    result = process_video_with_cache(face_processor, cache_manager, input_path, "视频处理")

    # 检查处理结果
    if not result.success or not result.valid_frames:
        raise RuntimeError("未能收集到任何帧")

    # 生成WebP动画（帧索引获取和分辨率解析在generate_webp_animation内部完成）
    success = generate_webp_animation(input_path, result, output_path, resolution=resolution)
    if not success:
        raise RuntimeError("WebP动画生成失败")

    # 返回webp_generator兼容格式
    return {
        "success": True,
        "output_path": output_path,
    }

def analyze_and_generate_single_webp(face_processor: FaceProcessor, cache_manager: FaceAnalyzerCacheManager,
                                    input_path: str, output_path: str,
                                    resolution: Optional[str] = None) -> Dict[str, Any]:
    """
    分析视频并生成单帧WebP图片 - webp_generator兼容接口

    Args:
        face_processor: 人脸处理器实例
        cache_manager: 缓存管理器实例
        input_path: 输入视频路径
        output_path: 输出WebP文件路径
        resolution: 可选的分辨率参数，格式如 "640x480"

    Returns:
        dict: webp_generator兼容的响应格式
    """
    # 使用统一的视频处理逻辑（复用BatchManager的相同流程）
    result = process_video_with_cache(face_processor, cache_manager, input_path, "视频处理(单帧)")

    # 检查处理结果
    if not result.success or not result.valid_frames:
        raise RuntimeError("未能获取到目标帧")

    # 生成单帧WebP（帧索引获取和分辨率解析在generate_single_webp内部完成）
    success = generate_single_webp(input_path, result, output_path, resolution=resolution)
    if not success:
        raise RuntimeError("单帧WebP生成失败")

    # 返回webp_generator兼容格式
    return {
        "success": True,
        "output_path": output_path
    }

