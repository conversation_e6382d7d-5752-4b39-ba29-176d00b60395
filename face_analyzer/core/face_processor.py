#!/usr/bin/env python3
"""
人脸处理器模块
"""

import sys
import os
import threading
import traceback
import tempfile
import subprocess
import multiprocessing
import logging
from typing import List


# 添加项目根目录到Python路径以便导入models
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from models.retinaface.detection import RetinaFaceDetection
from module.video_processor import create_video_processor
from classes.process_result import ProcessResult
from classes.frame_processing_result import FrameProcessingResult, STOP_SIGNAL
from classes.retinaface_result import RetinaFaceResult
from module.face_filter_chain import create_face_filter_chain
from module.frame_filter_chain import create_frame_filter_chain

from utils.image_utils import crop_frame_to_aspect_ratio
from config.settings import Config
model_path = f"{Config.RETINAFACE_MODEL_PATH}/pytorch_model.pt"
detector = RetinaFaceDetection(model_path, device=Config.DEVICE)

# 创建日志记录器
logger = logging.getLogger(__name__)

class FaceProcessor:
    """人脸处理器类 - 负责单个视频文件的完整处理流程"""

    def __init__(self):
        """
        初始化人脸处理器
        """
        # 初始化检测器和验证器

        self.detector = detector

        # 初始化处理组件
        self.video_processor = create_video_processor(Config.VIDEO_PROCESSOR_TYPE)

        # 初始化帧过滤器链
        self.frame_filter_chain = create_frame_filter_chain()

        # 初始化人脸过滤链
        self.face_filter_chain = create_face_filter_chain()

    def detect_faces(self, frame):
        """
        检测人脸并转换为RetinaFaceResult实例列表

        Args:
            frame: 输入帧

        Returns:
            List[RetinaFaceResult]: RetinaFaceResult实例列表
        """
        input_data = {'img': frame}
        detection_result = self.detector(input_data)

        # 将检测结果转换为RetinaFaceResult实例列表
        face_results = []

        if detection_result is not None and len(detection_result) == 2:
            dets, landms = detection_result

            if dets is not None and len(dets) > 0:
                for i in range(len(dets)):
                    det = dets[i]
                    if len(det) < 5:
                        continue

                    x1, y1, x2, y2, confidence = det[:5]

                    # 获取对应的关键点
                    landmarks = None
                    if landms is not None and i < len(landms):
                        landmarks = landms[i]

                    if landmarks is not None:
                        # 创建RetinaFaceResult实例
                        face_result = RetinaFaceResult(
                            face_id=i + 1,
                            confidence=float(confidence),
                            bbox=[float(x1), float(y1), float(x2), float(y2)],
                            landmarks=landmarks
                        )
                        face_results.append(face_result)

        return face_results

    def process_single_video(self, video_path: str, thread_id: str = "main") -> ProcessResult:
        """
        处理单个视频文件的完整流程

        Args:
            video_path (str): 视频文件路径
            thread_id (str): 线程ID，用于日志标识

        Returns:
            ProcessResult: 处理结果
        """
        # 创建 FrameProcessingResult 实例
        processing_result = FrameProcessingResult()

        # 调用新的 find_valid_frame 方法
        self.find_valid_frame(video_path, processing_result, search_all=False, thread_id=thread_id)

        # 使用 _build_result 直接构造并返回 ProcessResult
        result = self._build_result(processing_result, video_path)

        return result

    def find_valid_frame(self, video_path: str, processing_result: FrameProcessingResult, search_all: bool = False, thread_id: str = "main") -> None:
        """
        在视频中找到有效几何形状的人脸（支持单帧和全搜索模式）

        Args:
            video_path (str): 视频文件路径
            processing_result (FrameProcessingResult): 处理结果对象，结果将直接写入此对象
            search_all (bool): 搜索模式
                - False: 找到第一个有效帧即退出（默认）
                - True: 完整搜索整个视频中的所有有效帧
            thread_id (str): 线程标识符

        Returns:
            None: 结果直接写入 processing_result 对象
        """
        producer_id = f"{thread_id}_producer"
        consumer_id = f"{thread_id}_consumer"

        # 创建生产者和消费者线程
        producer_thread = threading.Thread(
            target=self._frame_producer,
            args=(video_path, processing_result, producer_id)
        )
        consumer_thread = threading.Thread(
            target=self._frame_consumer,
            args=(processing_result, search_all, consumer_id)
        )

        # 启动线程
        producer_thread.start()
        consumer_thread.start()

        # 等待线程完成
        producer_thread.join()
        consumer_thread.join()

    def _frame_producer(self, video_path: str, processing_result: FrameProcessingResult, thread_id: str):
        """
        生产者线程：负责视频帧读取和预处理

        Args:
            video_path (str): 视频文件路径
            processing_result (FrameProcessingResult): 处理结果对象
            thread_id (str): 线程标识符
        """
        video_iterator = self.video_processor(video_path)

        for frame, frame_info in video_iterator:
            # 检查停止信号
            if processing_result.stop_event.is_set():
                break

            # 帧过滤预处理
            is_valid, _ = self.frame_filter_chain(frame, frame_info)

            if not is_valid:
                continue

            # 队列写入
            frame_data = {
                'frame': frame,
                'frame_info': frame_info,
                'is_stop_signal': False
            }

            processing_result.frame_queue.put(frame_data)

        # 发送停止信号
        processing_result.frame_queue.put(STOP_SIGNAL)

    def _frame_consumer(self, processing_result: FrameProcessingResult, search_all: bool, thread_id: str):
        """
        消费者线程：负责人脸检测和过滤处理

        Args:
            processing_result (FrameProcessingResult): 处理结果对象
            search_all (bool): 是否搜索所有帧
            thread_id (str): 线程标识符
        """
        while True:
            frame_data = processing_result.frame_queue.get()

            # 检查停止信号
            if frame_data['is_stop_signal']:
                break

            # 处理帧数据 - 直接集成单帧处理逻辑
            frame_index = frame_data['frame_info']['global_idx']

            # 图像裁剪处理 - 裁剪为正方形（1:1宽高比）
            cropped_frame = crop_frame_to_aspect_ratio(frame_data['frame'], 1.0)

            # 人脸检测（已包含转换逻辑）
            face_results = self.detect_faces(cropped_frame)

            if face_results:
                # 使用人脸过滤链进行过滤
                highest_score, _ = self.face_filter_chain(cropped_frame, face_results)

                # 线程安全地添加有效帧（按得分分组）
                with processing_result.lock:
                    processing_result.valid_frames[highest_score].append(frame_index)

                    # 早期退出逻辑（仅在非全搜索模式）
                    if highest_score >= Config.FACEFILTER_THRESHOLD and not search_all:
                        processing_result.stop_event.set()
                        break



    def _build_result(self, processing_result: FrameProcessingResult, video_path: str) -> ProcessResult:
        """构造返回结果，直接返回 ProcessResult 对象"""
        if processing_result.valid_frames:
            return ProcessResult(
                success=True,
                video_path=video_path,
                valid_frames=dict(processing_result.valid_frames)  # 直接使用完整的得分分组字典
            )
        else:
            return ProcessResult(
                success=False,
                video_path=video_path,
                valid_frames={}  # 失败时返回空字典
            )

    def process_video_with_segmentation(self, video_path: str) -> ProcessResult:
        """
        处理视频文件，对长视频（>5分钟）进行分割并行处理

        Args:
            video_path (str): 视频文件路径

        Returns:
            ProcessResult: 处理结果
        """
        # 1. 检测视频时长
        duration = self._get_video_duration(video_path)

        # 2. 判断是否需要分割
        if duration <= 60 or Config.VIDEO_SPLIT_SEGMENTS <= 1:
            return self.process_single_video(video_path, "single")

        # 3. 长视频分割并行处理
        return self._process_long_video(video_path, duration)

    def _get_video_duration(self, video_path: str) -> float:
        """
        获取视频时长（秒）

        Args:
            video_path (str): 视频文件路径

        Returns:
            float: 视频时长（秒）
        """
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            import json
            data = json.loads(result.stdout)
            duration = float(data['format']['duration'])
            return duration

        except Exception as e:
            logger.error(f"视频编解码错误: 无法获取视频时长 {video_path}: {e}")
            return 0.0

    def _process_long_video(self, video_path: str, duration: float) -> ProcessResult:
        """
        处理长视频：分割为多个片段并行处理

        Args:
            video_path (str): 视频文件路径
            duration (float): 视频时长（秒）

        Returns:
            ProcessResult: 合并后的处理结果
        """
        segment_duration = duration / Config.VIDEO_SPLIT_SEGMENTS
        logger.info(f"将视频分割为{Config.VIDEO_SPLIT_SEGMENTS}个片段，每段 {segment_duration:.1f}s")

        with tempfile.TemporaryDirectory() as temp_dir:
            logger.debug(f"临时目录: {temp_dir}")

            # 1. 分割视频
            segment_paths = self._split_video(video_path, temp_dir, segment_duration)

            # 2. 并行处理
            results = self._process_segments_parallel(segment_paths)

            # 3. 合并结果
            final_result = self._merge_results(results, video_path, segment_duration)

            return final_result

    def _split_video(self, video_path: str, temp_dir: str, segment_duration: float) -> List[str]:
        """
        使用FFmpeg分割视频为多个片段

        Args:
            video_path (str): 原视频路径
            temp_dir (str): 临时目录
            segment_duration (float): 每段时长（秒）

        Returns:
            List[str]: 分割后的视频文件路径列表
        """
        segment_paths = []

        for i in range(Config.VIDEO_SPLIT_SEGMENTS):
            start_time = i * segment_duration
            output_path = os.path.join(temp_dir, f"segment_{i:02d}.mp4")

            cmd = [
                'ffmpeg', '-y', '-v', 'quiet',
                '-ss', str(start_time),
                '-t', str(segment_duration),
                '-i', video_path,
                '-c', 'copy',  # 使用流复制，避免重新编码
                '-avoid_negative_ts', 'make_zero',
                output_path
            ]

            try:
                subprocess.run(cmd, check=True)
                segment_paths.append(output_path)
                logger.info(f"片段 {i+1}/{Config.VIDEO_SPLIT_SEGMENTS} 分割完成: {os.path.basename(output_path)}")
            except subprocess.CalledProcessError as e:
                logger.error(f"视频编解码错误: 片段 {i+1} 分割失败: {e}")

        logger.info(f"成功分割 {len(segment_paths)}/{Config.VIDEO_SPLIT_SEGMENTS} 个片段")
        return segment_paths

    def _process_segments_parallel(self, segment_paths: List[str]) -> List[ProcessResult]:
        """
        并行处理视频片段

        Args:
            segment_paths (List[str]): 视频片段路径列表

        Returns:
            List[ProcessResult]: 处理结果列表
        """
        if not segment_paths:
            return []

        # 设置multiprocessing启动方法为spawn以避免CUDA问题
        original_start_method = multiprocessing.get_start_method()
        multiprocessing.set_start_method('spawn', force=True)

        # 创建进程池，最大进程数由配置决定
        max_workers = Config.VIDEO_SPLIT_SEGMENTS
        logger.info(f"启动 {max_workers} 个并行进程处理 {len(segment_paths)} 个片段")

        # 准备参数：(segment_path, thread_id)
        args_list = [(path, f"seg_{i:02d}") for i, path in enumerate(segment_paths)]

        with multiprocessing.Pool(processes=max_workers) as pool:
            results = pool.starmap(self._process_single_segment, args_list)

        successful_results = [r for r in results if r.success]
        failed_count = len(results) - len(successful_results)

        logger.info(f"并行处理完成: {len(successful_results)} 成功, {failed_count} 失败")

        # 恢复原始启动方法
        multiprocessing.set_start_method(original_start_method, force=True)

        return results

    @staticmethod
    def _process_single_segment(segment_path: str, thread_id: str) -> ProcessResult:
        """
        处理单个视频片段（静态方法，用于多进程）

        Args:
            segment_path (str): 片段路径
            thread_id (str): 线程ID

        Returns:
            ProcessResult: 处理结果
        """
        # 创建新的FaceProcessor实例（每个进程独立）
        processor = FaceProcessor()
        return processor.process_single_video(segment_path, thread_id)

    def _merge_results(self, results: List[ProcessResult], original_video_path: str,
                      segment_duration: float) -> ProcessResult:
        """
        合并多个片段的处理结果

        Args:
            results (List[ProcessResult]): 片段处理结果列表
            original_video_path (str): 原始视频路径
            segment_duration (float): 每段时长（秒）

        Returns:
            ProcessResult: 合并后的最终结果
        """
        if not results:
            return ProcessResult(
                success=False,
                video_path=original_video_path,
                valid_frames={}
            )

        # 统计成功的结果
        successful_results = [r for r in results if r.success]

        if not successful_results:
            return ProcessResult(
                success=False,
                video_path=original_video_path,
                valid_frames={}
            )

        logger.info(f"合并 {len(successful_results)}/{len(results)} 个成功的片段结果")

        # 合并 valid_frames，调整帧索引
        merged_valid_frames = {}

        # 获取原视频的FPS来计算帧偏移
        fps = self._get_video_fps(original_video_path)

        for i, result in enumerate(results):
            if not result.success:
                continue

            # 计算这个片段在原视频中的帧偏移量
            segment_start_time = i * segment_duration
            frame_offset = int(segment_start_time * fps)

            logger.debug(f"片段 {i}: 时间偏移 {segment_start_time:.1f}s, 帧偏移 {frame_offset}")

            # 调整帧索引并合并
            for score, frame_indices in result.valid_frames.items():
                if score not in merged_valid_frames:
                    merged_valid_frames[score] = []

                # 调整帧索引
                adjusted_indices = [idx + frame_offset for idx in frame_indices]
                merged_valid_frames[score].extend(adjusted_indices)

        # 对每个得分组的帧索引进行排序和去重
        for score in merged_valid_frames:
            merged_valid_frames[score] = sorted(list(set(merged_valid_frames[score])))

        # 统计结果
        total_frames = sum(len(indices) for indices in merged_valid_frames.values())
        logger.info(f"合并结果: 总共 {total_frames} 个有效帧")
        for score, indices in merged_valid_frames.items():
            logger.debug(f"  得分 {score}: {len(indices)} 个帧")

        return ProcessResult(
            success=True,
            video_path=original_video_path,
            valid_frames=merged_valid_frames
        )

    def _get_video_fps(self, video_path: str) -> float:
        """
        获取视频帧率

        Args:
            video_path (str): 视频文件路径

        Returns:
            float: 视频帧率
        """
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', '-select_streams', 'v:0', video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            import json
            data = json.loads(result.stdout)

            # 获取视频流的帧率
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    fps_str = stream.get('r_frame_rate', '25/1')
                    # 处理分数形式的帧率，如 "25/1"
                    if '/' in fps_str:
                        num, den = fps_str.split('/')
                        fps = float(num) / float(den)
                    else:
                        fps = float(fps_str)
                    return fps

            return 25.0

        except Exception as e:
            logger.error(f"视频编解码错误: 无法获取视频帧率 {video_path}: {e}")
            return 25.0


