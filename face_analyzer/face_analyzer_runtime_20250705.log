2025-07-05 01:44:31 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 01:46:22 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 01:48:19 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 01:51:21 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 01:51:21 - __main__ - INFO - 启动本地批处理模式...
2025-07-05 01:51:21 - __main__ - INFO - 输入目录: test_input
2025-07-05 01:51:21 - __main__ - INFO - 输出目录: test_output
2025-07-05 01:51:21 - __main__ - INFO - ============================================================
2025-07-05 01:51:25 - __main__ - INFO - 本地批处理完成
2025-07-05 01:53:30 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 01:53:30 - __main__ - INFO - ============================================================
2025-07-05 01:53:30 - __main__ - INFO - Face Analyzer API v1.0.0
2025-07-05 01:53:30 - __main__ - INFO - ============================================================
2025-07-05 01:53:30 - __main__ - INFO - 服务地址: http://127.0.0.1:5002
2025-07-05 01:53:30 - __main__ - INFO - 调试模式: 启用
2025-07-05 01:53:30 - __main__ - INFO - 兼容模式: webp_generator API
2025-07-05 01:53:30 - __main__ - INFO - ============================================================
2025-07-05 01:53:30 - __main__ - INFO - 可用端点:
2025-07-05 01:53:30 - __main__ - INFO -   POST /analyze          - 视频分析和WebP动画生成
2025-07-05 01:53:30 - __main__ - INFO -   POST /analyze_frame    - 视频分析和单帧WebP生成
2025-07-05 01:53:30 - __main__ - INFO - ============================================================
2025-07-05 01:53:30 - __main__ - INFO - 服务器启动中...
2025-07-05 01:53:30 - __main__ - INFO - 正在初始化Face Analyzer HTTP服务器...
2025-07-05 01:53:33 - __main__ - INFO - 服务器初始化完成
2025-07-05 01:53:33 - __main__ - INFO - 启动HTTP服务器: 127.0.0.1:5002
2025-07-05 01:53:33 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5002
2025-07-05 01:53:33 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-05 01:53:33 - werkzeug - INFO -  * Restarting with stat
2025-07-05 01:53:36 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 01:53:36 - __main__ - INFO - ============================================================
2025-07-05 01:53:36 - __main__ - INFO - Face Analyzer API v1.0.0
2025-07-05 01:53:36 - __main__ - INFO - ============================================================
2025-07-05 01:53:36 - __main__ - INFO - 服务地址: http://127.0.0.1:5002
2025-07-05 01:53:36 - __main__ - INFO - 调试模式: 启用
2025-07-05 01:53:36 - __main__ - INFO - 兼容模式: webp_generator API
2025-07-05 01:53:36 - __main__ - INFO - ============================================================
2025-07-05 01:53:36 - __main__ - INFO - 可用端点:
2025-07-05 01:53:36 - __main__ - INFO -   POST /analyze          - 视频分析和WebP动画生成
2025-07-05 01:53:36 - __main__ - INFO -   POST /analyze_frame    - 视频分析和单帧WebP生成
2025-07-05 01:53:36 - __main__ - INFO - ============================================================
2025-07-05 01:53:36 - __main__ - INFO - 服务器启动中...
2025-07-05 01:53:36 - __main__ - INFO - 正在初始化Face Analyzer HTTP服务器...
2025-07-05 01:55:36 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 01:57:20 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 08:36:05 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 08:36:05 - __main__ - INFO - 启动本地批处理模式...
2025-07-05 08:36:05 - __main__ - INFO - 输入目录: /home/<USER>/video_summerization/test
2025-07-05 08:36:05 - __main__ - INFO - 输出目录: real_test_output
2025-07-05 08:36:05 - __main__ - INFO - ============================================================
2025-07-05 13:22:50 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 13:22:50 - __main__ - INFO - 启动本地批处理模式...
2025-07-05 13:22:50 - __main__ - INFO - 输入目录: /home/<USER>/video_summerization/test
2025-07-05 13:22:50 - __main__ - INFO - 输出目录: real_test_output
2025-07-05 13:22:50 - __main__ - INFO - ============================================================
2025-07-05 13:23:21 - task.video_preview_generator - INFO - 选中 30 个关键帧: [492, 540, 588, 648, 696, 744, 792, 888, 936, 1080, 1128, 1176, 1236, 1284, 1332, 1428, 1476, 1572, 1668, 1764, 1824, 1872, 1968, 2064, 2160, 2208, 2256, 2352, 2412, 2460]
2025-07-05 13:23:21 - task.video_preview_generator - INFO - 计算出 15 个视频片段
2025-07-05 13:24:07 - task.video_preview_generator - INFO - 成功提取 15 个视频片段
2025-07-05 13:24:07 - task.video_preview_generator - INFO - 预览视频生成成功: real_test_output/test_preview.mp4
2025-07-05 13:24:07 - result_success - INFO - Successfully processed: /home/<USER>/video_summerization/test/test.mp4
2025-07-05 13:24:07 - core.batch_manager - INFO - 找到有效的帧索引：[2460]
2025-07-05 13:24:07 - __main__ - INFO - 本地批处理完成
2025-07-05 13:29:20 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 13:29:20 - __main__ - INFO - ============================================================
2025-07-05 13:29:20 - __main__ - INFO - Face Analyzer API v1.0.0
2025-07-05 13:29:20 - __main__ - INFO - ============================================================
2025-07-05 13:29:20 - __main__ - INFO - 服务地址: http://127.0.0.1:5003
2025-07-05 13:29:20 - __main__ - INFO - 调试模式: 启用
2025-07-05 13:29:20 - __main__ - INFO - 兼容模式: webp_generator API
2025-07-05 13:29:20 - __main__ - INFO - ============================================================
2025-07-05 13:29:20 - __main__ - INFO - 可用端点:
2025-07-05 13:29:20 - __main__ - INFO -   POST /analyze          - 视频分析和WebP动画生成
2025-07-05 13:29:20 - __main__ - INFO -   POST /analyze_frame    - 视频分析和单帧WebP生成
2025-07-05 13:29:20 - __main__ - INFO - ============================================================
2025-07-05 13:29:20 - __main__ - INFO - 服务器启动中...
2025-07-05 13:29:20 - __main__ - INFO - 正在初始化Face Analyzer HTTP服务器...
2025-07-05 13:29:23 - __main__ - INFO - 服务器初始化完成
2025-07-05 13:29:23 - __main__ - INFO - 启动HTTP服务器: 127.0.0.1:5003
2025-07-05 13:29:23 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5003
2025-07-05 13:29:23 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-05 13:29:23 - werkzeug - INFO -  * Restarting with stat
2025-07-05 13:29:25 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 13:29:25 - __main__ - INFO - ============================================================
2025-07-05 13:29:25 - __main__ - INFO - Face Analyzer API v1.0.0
2025-07-05 13:29:25 - __main__ - INFO - ============================================================
2025-07-05 13:29:25 - __main__ - INFO - 服务地址: http://127.0.0.1:5003
2025-07-05 13:29:25 - __main__ - INFO - 调试模式: 启用
2025-07-05 13:29:25 - __main__ - INFO - 兼容模式: webp_generator API
2025-07-05 13:29:25 - __main__ - INFO - ============================================================
2025-07-05 13:29:25 - __main__ - INFO - 可用端点:
2025-07-05 13:29:25 - __main__ - INFO -   POST /analyze          - 视频分析和WebP动画生成
2025-07-05 13:29:25 - __main__ - INFO -   POST /analyze_frame    - 视频分析和单帧WebP生成
2025-07-05 13:29:25 - __main__ - INFO - ============================================================
2025-07-05 13:29:25 - __main__ - INFO - 服务器启动中...
2025-07-05 13:29:25 - __main__ - INFO - 正在初始化Face Analyzer HTTP服务器...
2025-07-05 13:29:28 - __main__ - INFO - 服务器初始化完成
2025-07-05 13:29:28 - __main__ - INFO - 启动HTTP服务器: 127.0.0.1:5003
2025-07-05 13:29:28 - werkzeug - WARNING -  * Debugger is active!
2025-07-05 13:29:28 - werkzeug - INFO -  * Debugger PIN: 132-337-058
2025-07-05 14:15:28 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 14:15:28 - __main__ - INFO - 启动本地批处理模式...
2025-07-05 14:15:28 - __main__ - INFO - 输入目录: test_search_all
2025-07-05 14:15:28 - __main__ - INFO - 输出目录: test_search_all
2025-07-05 14:15:28 - __main__ - INFO - ============================================================
2025-07-05 14:15:31 - __main__ - INFO - 本地批处理完成
2025-07-05 14:21:06 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 14:21:06 - __main__ - INFO - ============================================================
2025-07-05 14:21:06 - __main__ - INFO - Face Analyzer API v1.0.0
2025-07-05 14:21:06 - __main__ - INFO - ============================================================
2025-07-05 14:21:06 - __main__ - INFO - 服务地址: http://0.0.0.0:5001
2025-07-05 14:21:06 - __main__ - INFO - 调试模式: 禁用
2025-07-05 14:21:06 - __main__ - INFO - 兼容模式: webp_generator API
2025-07-05 14:21:06 - __main__ - INFO - ============================================================
2025-07-05 14:21:06 - __main__ - INFO - 可用端点:
2025-07-05 14:21:06 - __main__ - INFO -   POST /analyze          - 视频分析和WebP动画生成
2025-07-05 14:21:06 - __main__ - INFO -   POST /analyze_frame    - 视频分析和单帧WebP生成
2025-07-05 14:21:06 - __main__ - INFO - ============================================================
2025-07-05 14:21:06 - __main__ - INFO - 服务器启动中...
2025-07-05 14:21:06 - __main__ - INFO - 正在初始化Face Analyzer HTTP服务器...
2025-07-05 14:21:09 - __main__ - INFO - 服务器初始化完成
2025-07-05 14:21:09 - __main__ - INFO - 启动HTTP服务器: 0.0.0.0:5001
2025-07-05 14:21:09 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-07-05 14:21:09 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-05 14:21:49 - core.api_manager - INFO - 开始处理请求: /home/<USER>/video_summerization/test/test.mp4 -> /home/<USER>/video_summerization/test_script/face_analyzer/0.webp, 分辨率: 642x360
2025-07-05 14:21:49 - core.api_helper - INFO - 开始视频处理: /home/<USER>/video_summerization/test/test.mp4
2025-07-05 14:36:00 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 14:36:00 - __main__ - INFO - ============================================================
2025-07-05 14:36:00 - __main__ - INFO - Face Analyzer API v1.0.0
2025-07-05 14:36:00 - __main__ - INFO - ============================================================
2025-07-05 14:36:00 - __main__ - INFO - 服务地址: http://0.0.0.0:5001
2025-07-05 14:36:00 - __main__ - INFO - 调试模式: 禁用
2025-07-05 14:36:00 - __main__ - INFO - 兼容模式: webp_generator API
2025-07-05 14:36:00 - __main__ - INFO - ============================================================
2025-07-05 14:36:00 - __main__ - INFO - 可用端点:
2025-07-05 14:36:00 - __main__ - INFO -   POST /analyze          - 视频分析和WebP动画生成
2025-07-05 14:36:00 - __main__ - INFO -   POST /analyze_frame    - 视频分析和单帧WebP生成
2025-07-05 14:36:00 - __main__ - INFO - ============================================================
2025-07-05 14:36:00 - __main__ - INFO - 服务器启动中...
2025-07-05 14:36:00 - __main__ - INFO - 正在初始化Face Analyzer HTTP服务器...
2025-07-05 14:36:04 - __main__ - INFO - 服务器初始化完成
2025-07-05 14:36:04 - __main__ - INFO - 启动HTTP服务器: 0.0.0.0:5001
2025-07-05 14:36:04 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://*************:5001
2025-07-05 14:36:04 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-05 14:36:07 - core.api_manager - INFO - 开始处理请求: /home/<USER>/video_summerization/test/test.mp4 -> /home/<USER>/video_summerization/test_script/face_analyzer/0.webp, 分辨率: 642x360
2025-07-05 14:36:07 - core.api_helper - INFO - 开始视频处理: /home/<USER>/video_summerization/test/test.mp4
2025-07-05 14:36:08 - core.face_processor - INFO - 将视频分割为4个片段，每段 2118.5s
2025-07-05 14:36:15 - core.face_processor - INFO - 片段 1/4 分割完成: segment_00.mp4
2025-07-05 14:36:21 - core.face_processor - INFO - 片段 2/4 分割完成: segment_01.mp4
2025-07-05 14:36:28 - core.face_processor - INFO - 片段 3/4 分割完成: segment_02.mp4
2025-07-05 14:36:35 - core.face_processor - INFO - 片段 4/4 分割完成: segment_03.mp4
2025-07-05 14:36:35 - core.face_processor - INFO - 成功分割 4/4 个片段
2025-07-05 14:36:35 - core.face_processor - INFO - 启动 4 个并行进程处理 4 个片段
2025-07-05 14:36:37 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 14:36:37 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 14:36:37 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 14:36:37 - root - INFO - 运行时日志服务初始化完成，级别: INFO
2025-07-05 14:45:46 - core.face_processor - INFO - 并行处理完成: 4 成功, 0 失败
2025-07-05 14:45:46 - core.face_processor - INFO - 合并 4/4 个成功的片段结果
2025-07-05 14:45:47 - core.face_processor - INFO - 合并结果: 总共 1619 个有效帧
2025-07-05 14:45:47 - core.api_helper - INFO - 视频处理结果: success=True, valid_frames=5
2025-07-05 14:45:50 - core.api_manager - INFO - 处理完成: {'success': True, 'output_path': '/home/<USER>/video_summerization/test_script/face_analyzer/0.webp'}
2025-07-05 14:45:50 - werkzeug - INFO - 127.0.0.1 - - [05/Jul/2025 14:45:50] "POST /analyze HTTP/1.1" 200 -
