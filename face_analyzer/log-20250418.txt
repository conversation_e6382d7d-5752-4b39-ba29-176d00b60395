[INPUT ] ffmpeg -i ../../test/test.mp4 -vcodec libwebp -t 5 -lossless 0 -qscale 75 -preset default -loop 0 -vf scale=642:360,fps=24 -an -vsync 0
[RESOLUTION] 从 -vf 参数捕捉到分辨率: 642x360
[WEBP_PROCESSING] 检测到 libwebp 使用和 -t 选项，尝试视频分析服务器（动画模式）
[WEBP_PROCESSING] 自动添加 .webp 扩展名: 0.webp
[VIDEO_ANALYSIS] 开始视频分析: ../../test/test.mp4 -> 0.webp (分辨率: 642x360)
[VIDEO_ANALYSIS] 发送请求到: http://localhost:5001/analyze
[RETRY] 开始 视频分析
[VIDEO_ANALYSIS] 视频分析完成
[VIDEO_ANALYSIS] 输出文件已生成: /home/<USER>/video_summerization/test_script/face_analyzer/0.webp (大小: 1.0K)
[WEBP_PROCESSING] 动画分析服务器处理成功，跳过 FFmpeg 处理
