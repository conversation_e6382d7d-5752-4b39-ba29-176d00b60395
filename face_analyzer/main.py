#!/usr/bin/env python3
"""
Face Analyzer 统一入口程序
支持本地批处理模式和HTTP API服务器模式
"""

import argparse
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

logger = logging.getLogger(__name__)

# 初始化运行时日志服务
from utils.logging_service import RuntimeLoggingService
from config.settings import Config

runtime_logger = RuntimeLoggingService()
runtime_logger.setup_logging()


def setup_local_mode_parser(subparsers):
    """设置本地批处理模式的参数解析器"""
    local_parser = subparsers.add_parser(
        'local',
        help='本地批处理模式 - 批量处理视频文件',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
本地批处理模式示例:
  python main.py local input_dir output_dir
  python main.py local /path/to/videos /path/to/output
        """
    )

    local_parser.add_argument(
        'input_base',
        help='输入目录路径，包含待处理的视频文件'
    )
    local_parser.add_argument(
        'output_base',
        help='输出目录路径，处理结果将保存在此目录'
    )

    return local_parser


def setup_server_mode_parser(subparsers):
    """设置HTTP服务器模式的参数解析器"""
    server_parser = subparsers.add_parser(
        'server',
        help='HTTP API服务器模式 - 启动webp_generator兼容的HTTP服务',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
HTTP服务器模式示例:
  python main.py server                    # 使用默认配置启动服务器
  python main.py server --host 0.0.0.0    # 指定主机地址
  python main.py server --port 8080       # 指定端口
  python main.py server --debug           # 启用调试模式
        """
    )

    # 导入配置获取默认值
    from config.settings import Config
    default_host = Config.HTTP_HOST
    default_port = Config.HTTP_PORT
    default_debug = Config.HTTP_DEBUG
    default_log_level = Config.LOG_LEVEL

    server_parser.add_argument(
        '--host',
        type=str,
        default=default_host,
        help=f'服务器主机地址 (默认: {default_host})'
    )

    server_parser.add_argument(
        '--port',
        type=int,
        default=default_port,
        help=f'服务器端口 (默认: {default_port})'
    )

    server_parser.add_argument(
        '--debug',
        action='store_true',
        default=default_debug,
        help='启用调试模式'
    )

    server_parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default=default_log_level,
        help=f'日志级别 (默认: {default_log_level})'
    )

    server_parser.add_argument(
        '--test-mode',
        action='store_true',
        help='启用测试模式（无需完整依赖）'
    )

    return server_parser


def run_local_mode(args):
    """运行本地批处理模式"""
    logger.info("启动本地批处理模式...")
    logger.info(f"输入目录: {args.input_base}")
    logger.info(f"输出目录: {args.output_base}")
    logger.info("=" * 60)

    # 导入批处理管理器
    from core.batch_manager import BatchManager

    # 创建批处理管理器并处理视频
    processor = BatchManager()
    processor.process_videos(args.input_base, args.output_base)

    logger.info("本地批处理完成")


def setup_server_logging(log_level: str):
    """设置服务器模式的日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def print_server_startup_info(host: str, port: int, debug: bool):
    """打印服务器启动信息"""
    from config.settings import Config
    service_name = Config.API_SERVICE_NAME
    api_version = Config.API_VERSION

    logger.info("=" * 60)
    logger.info(f"{service_name} v{api_version}")
    logger.info("=" * 60)
    logger.info(f"服务地址: http://{host}:{port}")
    logger.info(f"调试模式: {'启用' if debug else '禁用'}")
    logger.info(f"兼容模式: webp_generator API")
    logger.info("=" * 60)
    logger.info("可用端点:")
    logger.info("  POST /analyze          - 视频分析和WebP动画生成")
    logger.info("  POST /analyze_frame    - 视频分析和单帧WebP生成")
    logger.info("=" * 60)
    logger.info("服务器启动中...")
def initialize_http_server():
    """初始化HTTP服务器"""
    logger.info("正在初始化Face Analyzer HTTP服务器...")

    # 直接导入API管理器模块
    from core.api_manager import create_app
    from utils.logging_service import RuntimeLoggingService
    from config.settings import Config

    # 初始化运行时日志服务
    runtime_logging_service = RuntimeLoggingService()
    runtime_logging_service.setup_logging()

    # 创建Flask应用
    app = create_app()

    # 配置Flask应用
    app.config['MAX_CONTENT_LENGTH'] = Config.HTTP_MAX_CONTENT_LENGTH

    logger.info("服务器初始化完成")
    return app


def run_test_server_mode(args):
    """运行测试服务器模式"""
    print("🧪 启动测试服务器模式...")
    print(f"📡 服务地址: http://{args.host}:{args.port}")
    print("⚠️  注意: 测试模式下核心功能不可用")
    print("=" * 60)

    # 导入并运行测试服务器
    import test_server
    test_server.run_test_server(host=args.host, port=args.port, debug=args.debug)


def run_server_mode(args):
    """运行HTTP服务器模式"""
    # 如果启用测试模式，使用测试服务器
    if getattr(args, 'test_mode', False):
        run_test_server_mode(args)
        return

    # 设置日志
    setup_server_logging(args.log_level)

    # 打印启动信息
    print_server_startup_info(args.host, args.port, args.debug)

    # 初始化服务器
    app = initialize_http_server()

    # 启动服务器
    logger.info(f"启动HTTP服务器: {args.host}:{args.port}")
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug
    )




def create_main_parser():
    """创建主参数解析器"""
    parser = argparse.ArgumentParser(
        description='Face Analyzer - 人脸分析工具统一入口',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用模式:
  local   - 本地批处理模式，批量处理视频文件
  server  - HTTP API服务器模式，提供webp_generator兼容接口

示例:
  # 本地批处理模式
  python main.py local input_dir output_dir

  # HTTP服务器模式
  python main.py server --host 0.0.0.0 --port 8080

  # 查看特定模式的帮助
  python main.py local --help
  python main.py server --help
        """
    )

    # 创建子命令解析器
    subparsers = parser.add_subparsers(
        dest='mode',
        help='运行模式选择',
        metavar='MODE'
    )

    # 设置本地模式和服务器模式的解析器
    setup_local_mode_parser(subparsers)
    setup_server_mode_parser(subparsers)

    return parser


def main():
    """主函数"""
    # 创建参数解析器
    parser = create_main_parser()

    # 解析命令行参数
    args = parser.parse_args()

    # 检查是否指定了模式
    if not args.mode:
        logger.error("错误: 必须指定运行模式")
        parser.print_help()
        sys.exit(1)

    # 根据模式运行相应功能
    if args.mode == 'local':
        run_local_mode(args)
    elif args.mode == 'server':
        run_server_mode(args)
    else:
        logger.error(f"错误: 未知的运行模式 '{args.mode}'")
        parser.print_help()
        sys.exit(1)


if __name__ == '__main__':
    main()
