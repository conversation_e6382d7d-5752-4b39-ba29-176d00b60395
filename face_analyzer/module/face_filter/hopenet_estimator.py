#!/usr/bin/env python3
"""
HopeNet头部姿态估计器模块
"""


# 估计头部姿态
# adjusted_bbox = draw_face_detection_result(
#     frame, face_info, x_offset, y_offset
# )
# pose_angles = self.hopenet_estimator.estimate_head_pose(frame, adjusted_bbox)
# deviation = self.hopenet_estimator.calculate_face_deviation(pose_angles)

# 绘制完整的可视化结果
# draw_complete_result(
#     frame, face_info, x_offset, y_offset, is_valid_quad, nose_inside,
#     inference_time, attempt_count, geometry_score, pose_angles, deviation
# )

import sys
import os
import torch
import torch.nn.functional as F
from torch.autograd import Variable
from torchvision import transforms
import torchvision
import numpy as np
import cv2
from PIL import Image
import logging

# 创建日志记录器
logger = logging.getLogger(__name__)

from config.settings import Config


class HopeNetEstimator:
    """HopeNet头部姿态估计器"""

    def __init__(self, enable=None, weights_path=None, device=None):
        """
        初始化HopeNet估计器

        Args:
            enable (bool): 是否启用HopeNet
            weights_path (str): 权重文件路径
            device (str): 设备
        """
        self.enable = enable if enable is not None else Config.ENABLE_HOPENET
        self.weights_path = weights_path or Config.HOPENET_WEIGHTS_PATH
        self.device = device or Config.DEVICE

        self.model = None
        self.transformations = None
        self.idx_tensor = None
        self.available = False
        self.hopenet_module = None  # 保存hopenet模块引用

        if self.enable:
            self.available = self._initialize_hopenet()
        else:
            pass

    def _initialize_hopenet(self):
        """初始化HopeNet模型"""
        # 保存当前的sys.path
        original_path = sys.path.copy()

        try:
            # 临时修改sys.path，将HopeNet路径放在最前面
            sys.path.insert(0, Config.DEEP_HEAD_POSE_PATH)

            # 清除可能的模块缓存以避免冲突
            modules_to_clear = ['utils', 'hopenet']
            for module_name in modules_to_clear:
                if module_name in sys.modules:
                    del sys.modules[module_name]

            # 使用importlib进行更精确的导入
            import importlib.util

            # 导入hopenet模块
            hopenet_spec = importlib.util.spec_from_file_location(
                "hopenet", os.path.join(Config.DEEP_HEAD_POSE_PATH, "hopenet.py")
            )
            hopenet = importlib.util.module_from_spec(hopenet_spec)
            hopenet_spec.loader.exec_module(hopenet)

            # 导入utils模块
            utils_spec = importlib.util.spec_from_file_location(
                "hopenet_utils", os.path.join(Config.DEEP_HEAD_POSE_PATH, "utils.py")
            )
            utils = importlib.util.module_from_spec(utils_spec)
            utils_spec.loader.exec_module(utils)

            # 保存模块引用
            self.hopenet_module = hopenet

        finally:
            # 恢复原始的sys.path
            sys.path = original_path


        # 初始化Hopenet模型
        self.model = self.hopenet_module.Hopenet(torchvision.models.resnet.Bottleneck, [3, 4, 6, 3], 66)

        # 加载模型权重
        if os.path.exists(self.weights_path):
            saved_state_dict = torch.load(self.weights_path, map_location=self.device)
            self.model.load_state_dict(saved_state_dict)
        else:
            logger.warning("Hopenet权重文件未找到，使用随机权重")

        # 移动模型到设备
        if torch.cuda.is_available():
            self.model.cuda()
        self.model.eval()

        # 设置变换
        self.transformations = transforms.Compose([
            transforms.Resize(224),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 设置索引张量用于姿态计算
        idx_list = [idx for idx in range(66)]
        if torch.cuda.is_available():
            self.idx_tensor = torch.FloatTensor(idx_list).cuda()
        else:
            self.idx_tensor = torch.FloatTensor(idx_list)

        return True

    def expand_bbox_dockerface_style(self, x_min, y_min, x_max, y_max, img_width, img_height, expansion=50):
        """使用dockerface风格扩展边界框"""
        # 应用固定像素扩展
        x_min -= expansion
        x_max += expansion
        y_min -= expansion
        y_max += int(expansion * 0.6)  # 底部扩展较少

        # 确保坐标在图像边界内
        x_min = max(x_min, 0)
        y_min = max(y_min, 0)
        x_max = min(img_width, x_max)
        y_max = min(img_height, y_max)

        return x_min, y_min, x_max, y_max

    def estimate_head_pose(self, image, bbox):
        """
        估计头部姿态

        Args:
            image: 输入图像
            bbox: 人脸边界框 [x1, y1, x2, y2]

        Returns:
            dict: 姿态角度字典 {'yaw': float, 'pitch': float, 'roll': float}
        """
        if not self.enable or not self.available:
            return None

        if self.model is None or self.transformations is None:
            return None

        try:
            x_min, y_min, x_max, y_max = map(int, bbox)
            img_height, img_width = image.shape[:2]

            # 扩展边界框
            expanded_x_min, expanded_y_min, expanded_x_max, expanded_y_max = self.expand_bbox_dockerface_style(
                x_min, y_min, x_max, y_max, img_width, img_height
            )

            # 裁剪人脸区域
            face_img = image[expanded_y_min:expanded_y_max, expanded_x_min:expanded_x_max]

            if face_img.size == 0:
                return None

            # 转换为RGB供PIL使用
            face_rgb = cv2.cvtColor(face_img, cv2.COLOR_BGR2RGB)
            face_pil = Image.fromarray(face_rgb)

            # 应用变换
            transformed_img = self.transformations(face_pil)
            img_shape = transformed_img.size()
            transformed_img = transformed_img.view(1, img_shape[0], img_shape[1], img_shape[2])

            # 移动到设备
            if torch.cuda.is_available():
                transformed_img = Variable(transformed_img).cuda()
            else:
                transformed_img = Variable(transformed_img)

            # 通过Hopenet前向传播
            with torch.no_grad():
                yaw, pitch, roll = self.model(transformed_img)

                # 应用softmax并获取预测
                yaw_predicted = F.softmax(yaw, dim=1)
                pitch_predicted = F.softmax(pitch, dim=1)
                roll_predicted = F.softmax(roll, dim=1)

                # 获取连续预测（度数）
                yaw_predicted = torch.sum(yaw_predicted.data[0] * self.idx_tensor) * 3 - 99
                pitch_predicted = torch.sum(pitch_predicted.data[0] * self.idx_tensor) * 3 - 99
                roll_predicted = torch.sum(roll_predicted.data[0] * self.idx_tensor) * 3 - 99

                # 转换为浮点数
                yaw_deg = float(yaw_predicted)
                pitch_deg = float(pitch_predicted)
                roll_deg = float(roll_predicted)

                return {
                    'yaw': yaw_deg,
                    'pitch': pitch_deg,
                    'roll': roll_deg
                }

        except Exception as e:
            logger.error(f"模型推理错误: 头部姿态估计失败: {e}")
            return None

    def calculate_face_deviation(self, pose_angles):
        """
        计算人脸偏离屏幕垂直方向的角度

        Args:
            pose_angles (dict): 姿态角度字典

        Returns:
            float: 偏离角度
        """
        if not self.enable or pose_angles is None:
            return None

        yaw = pose_angles['yaw']
        pitch = pose_angles['pitch']
        roll = pose_angles['roll']

        # 计算偏离垂直方向（Z轴垂直于屏幕）的角度
        # 对于直视摄像头的人脸：yaw≈0, pitch≈0, roll≈0
        deviation = np.sqrt(yaw**2 + pitch**2 + roll**2)

        # 限制在合理范围内
        deviation = min(deviation, 90.0)

        return deviation
