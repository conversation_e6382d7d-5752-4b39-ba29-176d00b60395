#!/usr/bin/env python3
"""
视频处理器模块
"""

import os
import cv2
import numpy as np
from abc import ABC, abstractmethod
from typing import Generator, Tuple, Dict, Any
from config.settings import Config

from decord import VideoReader, cpu

try:
    from mvextractor.videocap import VideoCap
    MVEXTRACTOR_AVAILABLE = True
except ImportError:
    MVEXTRACTOR_AVAILABLE = False
    VideoCap = None

class BaseVideoProcessor(ABC):
    """视频处理器抽象基类"""

    @abstractmethod
    def __init__(self):
        """初始化视频处理器"""
        pass

    @abstractmethod
    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧的抽象方法

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
                frame: 视频帧数据
                video_info_dict: 包含视频信息的字典，至少包含：
                    - global_idx: 全局帧索引
                    - fps: 帧率
                    - total_frames: 总帧数
        """
        pass


    def validate_video_path(self, video_path: str) -> bool:
        """
        验证视频文件路径是否有效

        Args:
            video_path (str): 视频文件路径

        Returns:
            bool: 路径是否有效
        """
        return os.path.isfile(video_path)


class CV2VideoProcessor(BaseVideoProcessor):
    """基于OpenCV的视频处理器类"""

    def __init__(self):
        pass

    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise IOError(f"无法打开视频: {video_path}")

        # 获取视频基本参数
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        sample_step = max(1, int(fps * Config.SAMPLE_INTERVAL_SEC))

        try:
            global_idx = 0

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if global_idx % sample_step == 0:
                    frame_info = {
                        'global_idx': global_idx,
                        'total_frames': total_frames,
                        'fps': fps
                    }
                    yield frame, frame_info

                global_idx += 1

                if global_idx >= total_frames:
                    break

        finally:
            cap.release()


class DecordVideoProcessor(BaseVideoProcessor):
    """基于Decord的视频处理器类"""

    def __init__(self):
        pass

    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
        """
        if not self.validate_video_path(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        try:
            # 使用decord打开视频
            vr = VideoReader(video_path, ctx=cpu(0), num_threads=16)

            # 获取视频基本参数
            fps = vr.get_avg_fps()
            total_frames = len(vr)

            sample_step = max(1, int(fps * Config.SAMPLE_INTERVAL_SEC))

            for i in range(0, len(vr), sample_step):
                try:
                    # 使用decord读取指定帧
                    frame = vr[i].asnumpy()

                    # 将RGB转换为BGR (decord默认返回RGB，而OpenCV使用BGR)
                    frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

                    frame_info = {
                        'global_idx': i,
                        'total_frames': total_frames,
                        'fps': fps
                    }

                    yield frame, frame_info

                except Exception as e:
                    # 如果读取某一帧失败，记录错误但继续处理下一帧
                    print(f"警告: 读取第 {i} 帧时出错: {e}")
                    continue

        except Exception as e:
            raise IOError(f"无法使用Decord打开视频: {video_path}, 错误: {e}")


class MVExtractorVideoProcessor(BaseVideoProcessor):
    """基于MVExtractor的视频处理器类，支持运动向量提取"""

    def __init__(self):
        if not MVEXTRACTOR_AVAILABLE:
            raise ImportError("MVExtractor library is not available. Please install it with: pip install motion-vector-extractor")
        self.cap = VideoCap()

    def calculate_motion_statistics(self, motion_vectors):
        """
        计算motion vector统计信息

        Args:
            motion_vectors (numpy.ndarray): motion vector数组

        Returns:
            dict: 包含运动统计信息
        """
        if len(motion_vectors) == 0:
            return {
                'mean_magnitude': 0.0,
            }

        # 计算每个motion vector的幅值
        # magnitudes = []

        # for mv in motion_vectors:
        #     # mv格式: [source, w, h, src_x, src_y, dst_x, dst_y, motion_x, motion_y, motion_scale]
        #     # 计算实际的运动向量
        #     dx = mv[7] / mv[9] if mv[9] != 0 else 0  # motion_x / motion_scale
        #     dy = mv[8] / mv[9] if mv[9] != 0 else 0  # motion_y / motion_scale

        #     magnitude = (dx * dx + dy * dy) ** 0.5
        #     magnitudes.append(magnitude)
        mv = np.asarray(motion_vectors, dtype=float)
        mx = mv[:, 7]
        my = mv[:, 8]
        ms = mv[:, 9]

        # 预分配结果数组
        num = mx*mx + my*my
        den = ms*ms
        magnitudes = np.empty_like(num)

        # 只有在 den != 0 时才做除法，其它位置填 0（或你期望的默认值）
        np.divide(num, den,
                out=magnitudes,      # 将结果写入 magnitudes
                where=den != 0)       # 仅在 den != 0 时执行

        # 经过化简的计算

        return {
            'mean_magnitude': magnitudes.mean(),
        }

    def __call__(self, video_path: str) -> Generator[Tuple[Any, Dict[str, Any]], None, None]:
        """
        从视频中提取帧和运动向量

        Args:
            video_path (str): 视频文件路径

        Yields:
            Tuple[Any, Dict[str, Any]]: (frame, video_info_dict)
        """
        try:
            # 使用mvextractor打开视频
            ret = self.cap.open(video_path)
            if not ret:
                raise IOError(f"无法使用MVExtractor打开视频: {video_path}")

            # 使用OpenCV获取视频信息
            import cv2
            temp_cap = cv2.VideoCapture(video_path)
            fps = temp_cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(temp_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            temp_cap.release()

            if fps <= 0:
                fps = 25.0  # 默认帧率

            # 智能帧选择参数
            window_size = int(Config.SAMPLE_INTERVAL_SEC * fps)
            frame_idx = 0

            # 滑动窗口处理
            while True:
                window_frames = []  # 存储窗口内的帧数据

                # 收集一个窗口的帧
                for _ in range(window_size):
                    ret, frame, motion_vectors, _, _ = self.cap.read()

                    if not ret:
                        # 视频结束，处理剩余帧（如果有）
                        break

                    try:
                        motion_stats = self.calculate_motion_statistics(motion_vectors)

                        window_frames.append({
                            'frame': frame,
                            'global_idx': frame_idx,
                            'motion_stats': motion_stats,
                        })

                    except Exception as e:
                        # 如果处理某一帧失败，记录错误但继续处理下一帧
                        print(f"警告: 处理第 {frame_idx} 帧时出错: {e}")

                    frame_idx += 1

                # 智能帧选择：选择运动幅值最小的帧
                if window_frames:
                    # 直接比较找到最小运动幅值的帧
                    selected_frame_data = window_frames[0]
                    min_magnitude = selected_frame_data['motion_stats'].get('mean_magnitude', 0.0)

                    for frame_data in window_frames[1:]:
                        mean_magnitude = frame_data['motion_stats'].get('mean_magnitude', 0.0)
                        if mean_magnitude < min_magnitude:
                            min_magnitude = mean_magnitude
                            selected_frame_data = frame_data

                    frame_info = {
                        'global_idx': selected_frame_data['global_idx'],
                        'total_frames': total_frames,
                        'fps': fps,
                        'motion_statistics': selected_frame_data['motion_stats'],
                    }

                    yield selected_frame_data['frame'], frame_info
                else:
                    break

                # 如果读取的帧数少于窗口大小，说明视频已结束
                if len(window_frames) < window_size:
                    break



        except Exception as e:
            raise IOError(f"无法使用MVExtractor处理视频: {video_path}, 错误: {e}")
        finally:
            try:
                self.cap.release()
            except Exception as e:
                print(f"警告: MVExtractor 资源释放失败: {e}")


def create_video_processor(processor_type: str = "decord") -> BaseVideoProcessor:
    """
    创建视频处理器的工厂函数

    Args:
        processor_type (str): 处理器类型，可选 "cv2"、"decord" 或 "mvextractor"，默认为 "decord"

    Returns:
        BaseVideoProcessor: 视频处理器实例

    Raises:
        ValueError: 不支持的处理器类型
        ImportError: 相关库不可用
    """
    if processor_type.lower() == "cv2":
        return CV2VideoProcessor()
    elif processor_type.lower() == "decord":
        return DecordVideoProcessor()
    elif processor_type.lower() == "mvextractor":
        return MVExtractorVideoProcessor()
    else:
        raise ValueError(f"不支持的处理器类型: {processor_type}. 支持的类型: 'cv2', 'decord', 'mvextractor'")

