#!/usr/bin/env python3
"""
视频处理器模块
"""

import os
import cv2
import numpy as np
from abc import ABC, abstractmethod
from typing import Generator, Tuple, Dict, Any
from config.settings import Config

from decord import VideoReader, cpu

try:
    from mvextractor.videocap import VideoCap
    MVEXTRACTOR_AVAILABLE = True
except ImportError:
    MVEXTRACTOR_AVAILABLE = False
    VideoCap = None


def calculate_timestamp(frame_idx: int, fps: float) -> str:
    """
    计算时间戳
    
    Args:
        frame_idx: 帧索引
        fps: 帧率
        
    Returns:
        str: 时间戳字符串
    """
    seconds = frame_idx / fps
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}"


class BaseVideoProcessor(ABC):
    """视频处理器基类"""
    
    @abstractmethod
    def __call__(self, video_path: str) -> Generator[Tuple[np.ndarray, Dict[str, Any]], None, None]:
        """
        处理视频并生成帧
        
        Args:
            video_path: 视频文件路径
            
        Yields:
            Tuple[np.ndarray, Dict[str, Any]]: (帧, 帧信息)
        """
        pass


class CV2VideoProcessor(BaseVideoProcessor):
    """基于OpenCV的视频处理器类"""
    
    def __init__(self):
        pass
    
    def __call__(self, video_path: str) -> Generator[Tuple[np.ndarray, Dict[str, Any]], None, None]:
        """
        使用OpenCV处理视频
        
        Args:
            video_path: 视频文件路径
            
        Yields:
            Tuple[np.ndarray, Dict[str, Any]]: (帧, 帧信息)
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        frame_idx = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_info = {
                'frame_idx': frame_idx,
                'fps': fps,
                'total_frames': total_frames,
                'timestamp': calculate_timestamp(frame_idx, fps)
            }
            
            yield frame, frame_info
            frame_idx += 1
        
        cap.release()


class DecordVideoProcessor(BaseVideoProcessor):
    """基于Decord的视频处理器类"""
    
    def __init__(self):
        pass
    
    def __call__(self, video_path: str) -> Generator[Tuple[np.ndarray, Dict[str, Any]], None, None]:
        """
        使用Decord处理视频
        
        Args:
            video_path: 视频文件路径
            
        Yields:
            Tuple[np.ndarray, Dict[str, Any]]: (帧, 帧信息)
        """
        try:
            vr = VideoReader(video_path, ctx=cpu(0))
            fps = vr.get_avg_fps()
            total_frames = len(vr)
            
            for frame_idx in range(total_frames):
                frame = vr[frame_idx].asnumpy()
                # 转换为BGR格式（OpenCV格式）
                frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                
                frame_info = {
                    'frame_idx': frame_idx,
                    'fps': fps,
                    'total_frames': total_frames,
                    'timestamp': calculate_timestamp(frame_idx, fps)
                }
                
                yield frame, frame_info
                
        except Exception as e:
            # 如果Decord失败，回退到OpenCV
            cv2_processor = CV2VideoProcessor()
            yield from cv2_processor(video_path)


class MVExtractorVideoProcessor(BaseVideoProcessor):
    """基于MVExtractor的视频处理器类，支持运动向量提取"""

    def __init__(self):
        if not MVEXTRACTOR_AVAILABLE:
            raise ImportError("MVExtractor library is not available. Please install it with: pip install motion-vector-extractor")
        self.cap = VideoCap()

    def calculate_motion_statistics(self, motion_vectors):
        """
        计算motion vector统计信息

        Args:
            motion_vectors (numpy.ndarray): motion vector数组

        Returns:
            dict: 包含运动统计信息
        """
        if len(motion_vectors) == 0:
            return {
                'mean_magnitude': 0.0,
            }

        # 计算运动幅值
        magnitudes = np.sqrt(motion_vectors[:, 2]**2 + motion_vectors[:, 3]**2)
        
        return {
            'max_magnitude': float(np.max(magnitudes)),
            'mean_magnitude': float(np.mean(magnitudes)),
            'median_magnitude': float(np.median(magnitudes)),
            'motion_count': len(magnitudes)
        }

    def __call__(self, video_path: str) -> Generator[Tuple[np.ndarray, Dict[str, Any]], None, None]:
        """
        使用MVExtractor处理视频并提取运动向量

        Args:
            video_path: 视频文件路径

        Yields:
            Tuple[np.ndarray, Dict[str, Any]]: (帧, 帧信息包含运动统计)
        """
        try:
            self.cap.open(video_path)
            fps = self.cap.get_fps()
            total_frames = self.cap.get_frame_count()
            
            frame_idx = 0
            while True:
                ret, frame, motion_vectors, frame_type, timestamp = self.cap.read()
                if not ret:
                    break
                
                # 计算运动统计
                motion_statistics = self.calculate_motion_statistics(motion_vectors)
                
                frame_info = {
                    'frame_idx': frame_idx,
                    'fps': fps,
                    'total_frames': total_frames,
                    'timestamp': calculate_timestamp(frame_idx, fps),
                    'motion_statistics': motion_statistics
                }
                
                yield frame, frame_info
                frame_idx += 1
                
        except Exception as e:
            # 如果MVExtractor失败，回退到Decord
            decord_processor = DecordVideoProcessor()
            yield from decord_processor(video_path)
        finally:
            if hasattr(self, 'cap'):
                self.cap.release()


def create_video_processor(processor_type: str = "decord") -> BaseVideoProcessor:
    """
    创建视频处理器的工厂函数

    Args:
        processor_type (str): 处理器类型，可选 "cv2"、"decord" 或 "mvextractor"，默认为 "decord"

    Returns:
        BaseVideoProcessor: 视频处理器实例

    Raises:
        ValueError: 不支持的处理器类型
        ImportError: 相关库不可用
    """
    if processor_type.lower() == "cv2":
        return CV2VideoProcessor()
    elif processor_type.lower() == "decord":
        return DecordVideoProcessor()
    elif processor_type.lower() == "mvextractor":
        return MVExtractorVideoProcessor()
    else:
        raise ValueError(f"不支持的处理器类型: {processor_type}. 支持的类型: 'cv2', 'decord', 'mvextractor'")
