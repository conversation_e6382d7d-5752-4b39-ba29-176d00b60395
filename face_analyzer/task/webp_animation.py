#!/usr/bin/env python3
"""
WebP动画生成模块
"""

import os
import logging
import cv2
import numpy as np
from typing import List, Optional
from PIL import Image

from config.settings import Config
from utils.image_utils import parse_resolution, resize_frame_with_aspect_ratio

logger = logging.getLogger(__name__)


def get_video_fps(video_path: str) -> float:
    """
    获取视频的真实帧率

    Args:
        video_path: 视频文件路径

    Returns:
        float: 视频帧率，如果获取失败返回0
    """
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return 0.0

        fps = cap.get(cv2.CAP_PROP_FPS)
        cap.release()

        # 验证FPS值的合理性
        if fps <= 0 or fps > 120:
            return 0.0

        return fps

    except Exception as e:
        logger.error(f"视频编解码错误: 无法获取视频FPS {video_path}: {e}")
        return 0.0


def generate_webp_animation(video_path: str, result, output_path: str,
                           duration_frames: Optional[int] = None,
                           resolution: Optional[str] = None) -> bool:
    """
    从视频处理结果生成WebP动画

    Args:
        video_path: 视频文件路径
        result: FaceProcessor的处理结果
        output_path: 输出WebP动画文件路径
        duration_frames: 动画帧数，如果为None则基于视频真实FPS计算
        resolution: 可选的分辨率参数，格式如 "640x480"

    Returns:
        bool: 生成是否成功
    """
    # 从处理结果中计算start_frame_idx
    if not result.success or not result.valid_frames:
        return False

    # 获取最高得分的第一个帧索引
    highest_score = max(result.valid_frames.keys())
    start_frame_idx = result.valid_frames[highest_score][0]

    if start_frame_idx is None:
        return False

    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    # 动态获取视频FPS
    video_fps = get_video_fps(video_path)
    if video_fps <= 0:
        return False

    # 计算动画帧数（基于视频真实FPS）
    if duration_frames is None:
        duration_frames = int(Config.WEBP_ANIMATION_DURATION_SEC * video_fps)

    # 收集动画帧
    frames = collect_animation_frames(video_path, start_frame_idx, duration_frames, resolution)

    if not frames:
        return False

    # 转换为PIL Images
    pil_images = []
    for frame in frames:
        # 转换为RGB格式（PIL需要）
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        pil_images.append(pil_image)

    # 计算每帧持续时间（毫秒），使用视频真实FPS
    frame_duration = int(1000 / video_fps)

    # 保存为WebP动画
    pil_images[0].save(
        output_path,
        'WEBP',
        save_all=True,
        append_images=pil_images[1:],
        duration=frame_duration,
        loop=Config.WEBP_ANIMATION_LOOP,
        quality=Config.WEBP_QUALITY,
        lossless=Config.WEBP_LOSSLESS
    )

    return True


def collect_animation_frames(video_path: str, start_frame_idx: int,
                           duration_frames: int, resolution: Optional[str] = None) -> List[np.ndarray]:
    """
    收集动画帧（连续帧收集，保持原始视频播放速度）

    Args:
        video_path: 视频文件路径
        start_frame_idx: 开始帧索引
        duration_frames: 需要收集的帧数（基于视频真实FPS计算）
        resolution: 可选的分辨率参数

    Returns:
        List[np.ndarray]: 收集到的帧列表
    """
    frames = []

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return frames

    # 获取视频总帧数
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 计算实际可收集的帧数
    max_available_frames = total_frames - start_frame_idx
    actual_frames = min(duration_frames, max_available_frames)

    if actual_frames <= 0:
        cap.release()
        return frames

    # 跳转到开始帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame_idx)

    # 连续收集帧（不跳帧，保持原始播放速度）
    for i in range(actual_frames):
        ret, frame = cap.read()
        if not ret:
            break

        # 调整分辨率
        if resolution:
            target_width, target_height = parse_resolution(resolution)
            frame = resize_frame_with_aspect_ratio(frame, target_width, target_height)
        elif Config.WEBP_DEFAULT_RESOLUTION:
            target_width, target_height = parse_resolution(Config.WEBP_DEFAULT_RESOLUTION)
            frame = resize_frame_with_aspect_ratio(frame, target_width, target_height)

        frames.append(frame)

    cap.release()

    return frames


def calculate_animation_frames(video_fps: float) -> int:
    """
    根据视频FPS计算动画帧数（基于配置的持续时间）

    Args:
        video_fps: 视频帧率

    Returns:
        int: 动画帧数
    """
    # 直接基于视频FPS和配置的持续时间计算帧数
    # 不再进行跳帧，保持原始播放速度
    animation_frames = int(Config.WEBP_ANIMATION_DURATION_SEC * video_fps)

    return max(1, animation_frames)



