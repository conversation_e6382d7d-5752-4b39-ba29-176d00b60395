#!/usr/bin/env python3
"""
单个WebP图片生成模块
"""

import os
import cv2
import numpy as np
from typing import Optional
from PIL import Image

from config.settings import Config
from utils.image_utils import parse_resolution, resize_frame_with_aspect_ratio


def generate_single_webp(video_path: str, result, output_path: str,
                        resolution: Optional[str] = None) -> bool:
    """
    从视频处理结果生成单个WebP图片

    Args:
        video_path: 视频文件路径
        result: FaceProcessor的处理结果
        output_path: 输出WebP文件路径
        resolution: 可选的分辨率参数，格式如 "640x480"

    Returns:
        bool: 生成是否成功
    """
    # 从处理结果中计算primary_frame_idx
    if not result.success or not result.valid_frames:
        return False

    # 获取最高得分的第一个帧索引
    highest_score = max(result.valid_frames.keys())
    primary_frame_idx = result.valid_frames[highest_score][0]

    if primary_frame_idx is None:
        return False

    # 直接使用OpenCV跳转到指定帧索引
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return False

    # 跳转到指定帧
    cap.set(cv2.CAP_PROP_POS_FRAMES, primary_frame_idx)

    # 读取帧
    ret, frame = cap.read()
    cap.release()

    if not ret:
        return False

    # 保存帧为WebP
    return save_frame_as_webp(frame, output_path, resolution)


def save_frame_as_webp(frame: np.ndarray, output_path: str, 
                      resolution: Optional[str] = None) -> bool:
    """
    将帧保存为WebP格式
    
    Args:
        frame: 输入帧
        output_path: 输出路径
        resolution: 可选的分辨率参数，格式如 "640x480"
    
    Returns:
        bool: 保存是否成功
    """
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    # 解析分辨率参数并调整帧大小
    if resolution:
        target_width, target_height = parse_resolution(resolution)
        frame = resize_frame_with_aspect_ratio(frame, target_width, target_height)
    elif Config.WEBP_DEFAULT_RESOLUTION:
        target_width, target_height = parse_resolution(Config.WEBP_DEFAULT_RESOLUTION)
        frame = resize_frame_with_aspect_ratio(frame, target_width, target_height)

    # 转换为RGB格式（PIL需要）
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

    # 转换为PIL Image
    pil_image = Image.fromarray(frame_rgb)

    # 保存为WebP
    pil_image.save(
        output_path,
        'WEBP',
        quality=Config.WEBP_QUALITY,
        lossless=Config.WEBP_LOSSLESS
    )

    return True



