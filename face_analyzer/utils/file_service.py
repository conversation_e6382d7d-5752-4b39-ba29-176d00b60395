#!/usr/bin/env python3
"""
文件服务模块
"""

import os
from config.settings import Config


def collect_video_files(input_base: str):
    """
    收集视频文件

    Args:
        input_base (str): 输入基础路径

    Returns:
        list: 视频文件路径列表
    """
    video_paths = []

    if os.path.isfile(input_base):
        # 如果输入是单个文件
        if input_base.lower().endswith(tuple(Config.SUPPORTED_VIDEO_EXTENSIONS)):
            video_paths.append(input_base)
    else:
        # 如果输入是目录
        for root, _, files in os.walk(input_base):
            for fname in files:
                if fname.lower().endswith(tuple(Config.SUPPORTED_VIDEO_EXTENSIONS)):
                    video_paths.append(os.path.join(root, fname))

    return video_paths


def load_processed_files(processed_file: str):
    """
    加载已处理的文件列表

    Args:
        processed_file (str): 已处理文件记录路径

    Returns:
        set: 已处理文件集合
    """
    processed_set = set()
    if os.path.exists(processed_file):
        with open(processed_file, 'r') as pf:
            processed_set = set(line.strip() for line in pf if line.strip())
    return processed_set


def mark_file_as_processed(processed_file: str, file_path: str):
    """
    标记文件为已处理

    Args:
        processed_file (str): 已处理文件记录路径
        file_path (str): 文件路径
    """
    with open(processed_file, 'a') as pf:
        pf.write(f"{file_path}\n")


def ensure_directory_exists(directory: str):
    """
    确保目录存在

    Args:
        directory (str): 目录路径
    """
    if directory and not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)


def get_output_path(input_path: str, input_base: str, output_base: str):
    """
    获取输出路径和基础文件名

    Args:
        input_path (str): 输入文件路径
        input_base (str): 输入基础路径
        output_base (str): 输出基础路径

    Returns:
        tuple: (输出目录, 基础文件名(无后缀))
    """
    fname = os.path.basename(input_path)
    base_name, _ = os.path.splitext(fname)

    # 判断input_base是文件还是目录（基于路径结构而非文件是否存在）
    if input_base == input_path or os.path.basename(input_base) == os.path.basename(input_path):
        # 如果input_base指向单个文件，直接保存到输出目录
        output_dir = output_base
    else:
        # 如果input_base是目录，保持目录结构
        root = os.path.dirname(input_path)
        rel_dir = os.path.relpath(root, input_base)
        # 处理相对路径中的 ".." 情况
        if rel_dir == ".":
            output_dir = output_base
        else:
            output_dir = os.path.join(output_base, rel_dir)

    return output_dir, base_name